{"overall_metrics": {"accuracy": 72.20721153846154, "macro_f1": 71.13537646877182, "kappa": 0.7104917868589744}, "model_complexity": {"macs": "4.998G", "parameters": "3.702M", "macs_raw": 4998385856.0, "params_raw": 3702247.0}, "inference_performance": {"avg_inference_time_ms": 0.05071872816636012, "std_inference_time_ms": 0.03054084778698218, "min_inference_time_ms": 0.03560259938240051, "max_inference_time_ms": 1.6278252005577087}, "dataset_info": {"total_samples": 208000, "dataset_type": "torchsig4096", "input_shape": [2, 4096], "num_classes": 25, "snr_range": [0.0, 30.0]}, "test_info": {"model_path": "./saved_models/awn/torchsig4096_20250619_151026/models/best_model.pth", "config_path": "config.yaml", "test_date": "2025-06-20 10:56:47"}}