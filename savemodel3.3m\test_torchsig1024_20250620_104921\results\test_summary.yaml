dataset_info:
  dataset_type: torchsig1024
  input_shape: !!python/tuple
  - 2
  - 1024
  num_classes: 25
  snr_range:
  - 0.0
  - 30.0
  total_samples: 208000
inference_performance:
  avg_inference_time_ms: 0.05194886372639583
  max_inference_time_ms: 0.8340626955032349
  min_inference_time_ms: 0.03452599048614502
  std_inference_time_ms: 0.02111060823807958
model_complexity:
  macs: 1.250G
  macs_raw: 1250250944.0
  parameters: 3.702M
  params_raw: 3702247.0
overall_metrics:
  accuracy: 65.36682692307693
  kappa: 0.6392377804487179
  macro_f1: 64.50392152091457
test_info:
  config_path: config.yaml
  model_path: ./saved_models/awn/torchsig1024_20250619_064215/models/best_model.pth
  test_date: '2025-06-20 10:50:25'
