import torch
import torch.nn as nn
import torch.nn.functional as F


class AWNLoss(nn.Module):
    """
    AWN自定义损失函数，结合交叉熵损失和小波提升方案正则化

    损失函数包含三个部分：
    1. 交叉熵损失：用于分类任务
    2. 高频损失：最小化高频分量绝对值，鼓励小波系数稀疏性
    3. 低频损失：保持低频分量与原信号均值相似

    综合公式：L = L_CE + λ₁∑|H| + λ₂∑‖L-L'‖₂
    """
    def __init__(self, lambda1=0.01, lambda2=0.01):
        """
        初始化损失函数

        参数:
            lambda1: 高频损失权重，控制稀疏性强度
            lambda2: 低频损失权重，控制信号重构质量
        """
        super(AWNLoss, self).__init__()
        # 交叉熵损失用于分类任务
        self.cross_entropy = nn.CrossEntropyLoss()
        # 高频损失权重
        self.lambda1 = lambda1
        # 低频损失权重
        self.lambda2 = lambda2

    def forward(self, model, outputs, targets, inputs):
        """
        计算总损失

        参数:
            model: AWN模型实例，用于计算提升方案损失
            outputs: 模型分类输出
            targets: 真实标签
            inputs: 输入数据，用于计算提升方案损失

        返回:
            total_loss: 综合损失
        """
        # 计算分类交叉熵损失
        ce_loss = self.cross_entropy(outputs, targets)

        # 计算小波提升方案正则化损失
        loss_H, loss_L = model.compute_lifting_loss(inputs)

        # 组合三个损失
        total_loss = ce_loss + self.lambda1 * loss_H + self.lambda2 * loss_L

        # 调试信息（仅在第一次调用时打印）
        if not hasattr(self, '_debug_printed'):
            print(f"AWN损失函数参数: lambda1={self.lambda1}, lambda2={self.lambda2}")
            print(f"损失组成: CE={ce_loss.item():.4f}, H={loss_H.item():.4f}, L={loss_L.item():.4f}")
            self._debug_printed = True

        return total_loss
