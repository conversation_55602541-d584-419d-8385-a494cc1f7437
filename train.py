#!/usr/bin/env python3
"""
AWN训练脚本

这个脚本用于训练AWN模型，支持多种数据集。
使用方法：python train.py
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import argparse
import numpy as np
from tqdm import tqdm
import logging
from datetime import datetime
import time
import json
from sklearn.metrics import f1_score, cohen_kappa_score

# 导入模型和工具
from models import AWN, AWNLoss
from utils.dataset import (
    load_rml_dataset, split_dataset, RML2016Dataset,
    get_hisar_data_loaders, get_torchsig_data_loaders, get_rml201801a_data_loaders
)

def setup_logging(output_dir):
    """设置日志"""
    os.makedirs(output_dir, exist_ok=True)
    log_file = os.path.join(output_dir, f'train_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)
def create_output_directories(config):
    """创建包含数据集和时间信息的输出目录结构"""
    dataset_type = config['data']['dataset_type']
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 基础输出目录
    base_output_dir = config['output_dir']

    # 创建细分的目录结构
    experiment_dir = os.path.join(base_output_dir, f"{dataset_type}_{timestamp}")

    # 创建子目录
    directories = {
        'experiment': experiment_dir,
        'models': os.path.join(experiment_dir, 'models'),
        'logs': os.path.join(experiment_dir, 'logs'),
        'results': os.path.join(experiment_dir, 'results'),
        'configs': os.path.join(experiment_dir, 'configs'),
        'plots': os.path.join(experiment_dir, 'plots')
    }

    # 创建所有目录
    for dir_path in directories.values():
        os.makedirs(dir_path, exist_ok=True)

    # 更新配置中的输出目录
    config['output_dir'] = experiment_dir
    config['directories'] = directories

    print(f"实验目录创建完成: {experiment_dir}")
    print(f"子目录包括: models, logs, results, configs, plots")

    return directories

def count_trainable_parameters(model):
    """计算模型的可训练参数数量"""
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in model.parameters())
    return trainable_params, total_params

def load_config(config_path='config.yaml'):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def get_data_loaders(config):
    """根据配置获取数据加载器"""
    dataset_type = config['data']['dataset_type']

    if dataset_type == 'rml':
        return get_rml_data_loaders(config)
    elif dataset_type == 'rml201801a':
        return get_rml201801a_data_loaders(config)
    elif dataset_type == 'hisar':
        return get_hisar_data_loaders(config)
    elif dataset_type.startswith('torchsig'):
        return get_torchsig_data_loaders(config)
    else:
        raise ValueError(f"不支持的数据集类型: {dataset_type}")

def get_rml_data_loaders(config):
    """获取RML数据集的数据加载器"""
    # 获取RML配置
    rml_config = config['data']
    file_path = rml_config['rml_file_path']

    # 获取调制类型
    if rml_config['modulations'] is None:
        modulations = config['rml_class_names']
    else:
        modulations = rml_config['modulations']

    # 加载数据
    X, labels, snrs = load_rml_dataset(
        file_path=file_path,
        modulations=modulations,
        samples_per_key=rml_config.get('samples_per_key')
    )

    # 使用新的三分割功能直接分割数据集
    (X_train, y_train, snr_train), (X_test, y_test, snr_test), (X_val, y_val, snr_val) = split_dataset(
        X, labels, snrs,
        train_ratio=config['data']['train_ratio'],
        test_ratio=config['data']['test_ratio'],
        val_ratio=config['data']['val_ratio'],
        seed=config['training']['seed'],
        stratify_by_snr=rml_config['stratify_by_snr']
    )

    print(f"RML数据集划分完成:")
    print(f"  训练集: {len(X_train)} 样本")
    print(f"  验证集: {len(X_val)} 样本")
    print(f"  测试集: {len(X_test)} 样本")

    # 验证分层采样效果
    print(f"训练集SNR分布: {np.unique(snr_train, return_counts=True)[1][:5]}...")
    print(f"验证集SNR分布: {np.unique(snr_val, return_counts=True)[1][:5]}...")

    # 创建数据集
    train_dataset = RML2016Dataset(X_train, y_train, snr_train)
    val_dataset = RML2016Dataset(X_val, y_val, snr_val)
    test_dataset = RML2016Dataset(X_test, y_test, snr_test)

    # 创建数据加载器
    batch_size = config['training']['batch_size']
    num_workers = config['training']['num_workers']

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=num_workers)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=num_workers)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=num_workers)

    return train_loader, val_loader, test_loader

def create_model(config):
    """创建模型"""
    model_config = config['model']
    dataset_type = config['data']['dataset_type']

    # 根据数据集类型调整参数
    if dataset_type == 'rml':
        num_classes = len(config['rml_class_names'])
        sequence_length = config['data']['sequence_lengths']['rml']
    elif dataset_type == 'rml201801a':
        num_classes = len(config['rml201801a_class_names'])
        sequence_length = config['data']['sequence_lengths']['rml201801a']
    elif dataset_type == 'hisar':
        num_classes = len(config['hisar_class_names'])
        sequence_length = config['data']['sequence_lengths']['hisar']
    elif dataset_type.startswith('torchsig'):
        num_classes = len(config['torchsig_class_names'])
        sequence_length = config['data']['sequence_lengths'][dataset_type]
    else:
        raise ValueError(f"不支持的数据集类型: {dataset_type}")

    # 获取数据集特定的模型参数
    dataset_params = model_config['dataset_specific_params'].get(dataset_type, {})
    wavelet_dim = dataset_params.get('wavelet_dim', 96)  # 默认值96
    decomposition_levels = dataset_params.get('decomposition_levels', 3)
    hidden_dim = dataset_params.get('hidden_dim', 512)
    dropout_rate = dataset_params.get('dropout_rate', 0.3)

    # 更新配置
    model_config['num_classes'] = num_classes
    model_config['sequence_length'] = sequence_length

    print(f"数据集: {dataset_type}")
    print(f"类别数: {num_classes}")
    print(f"序列长度: {sequence_length}")
    print(f"小波维度: {wavelet_dim}")
    print(f"分解级别: {decomposition_levels}")
    print(f"隐藏维度: {hidden_dim}")
    print(f"Dropout率: {dropout_rate}")

    # 创建模型 - 支持可配置的wavelet_dim参数
    model = AWN(
        num_classes=num_classes,
        decomposition_levels=decomposition_levels,
        signal_length=sequence_length,
        wavelet_dim=wavelet_dim
    )

    return model



def train_epoch(model, train_loader, criterion, optimizer, device, config, logger):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    correct = 0
    total = 0

    pbar = tqdm(train_loader, desc='Training')
    for batch_idx, (data, target, snr) in enumerate(pbar):
        data, target = data.to(device), target.to(device)

        optimizer.zero_grad()

        # 前向传播
        output = model(data)

        # 使用原始AWN的损失函数调用方式
        loss = criterion(model, output, target, data)

        # 反向传播
        loss.backward()

        # 梯度裁剪
        if config['training'].get('clip_grad', 0) > 0:
            torch.nn.utils.clip_grad_norm_(model.parameters(), config['training']['clip_grad'])

        optimizer.step()

        # 统计
        total_loss += loss.item()
        pred = output.argmax(dim=1, keepdim=True)
        correct += pred.eq(target.view_as(pred)).sum().item()
        total += target.size(0)

        # 更新进度条
        pbar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Acc': f'{100.*correct/total:.2f}%'
        })

    avg_loss = total_loss / len(train_loader)
    accuracy = 100. * correct / total

    logger.info(f'Train Loss: {avg_loss:.4f}, Train Acc: {accuracy:.2f}%')
    return avg_loss, accuracy

def validate(model, val_loader, criterion, device, logger):
    """验证模型"""
    model.eval()
    total_loss = 0
    correct = 0
    total = 0
    all_predictions = []
    all_targets = []

    with torch.no_grad():
        for data, target, snr in val_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            loss = criterion(model, output, target, data)

            total_loss += loss.item()
            pred = output.argmax(dim=1, keepdim=True)
            correct += pred.eq(target.view_as(pred)).sum().item()
            total += target.size(0)

            # 收集预测结果用于计算更多指标
            all_predictions.extend(pred.cpu().numpy().flatten())
            all_targets.extend(target.cpu().numpy())

    avg_loss = total_loss / len(val_loader)
    accuracy = 100. * correct / total

    # 计算额外的评估指标
    all_predictions = np.array(all_predictions)
    all_targets = np.array(all_targets)

    macro_f1 = f1_score(all_targets, all_predictions, average='macro') * 100
    kappa = cohen_kappa_score(all_targets, all_predictions)

    logger.info(f'Val Loss: {avg_loss:.4f}, Val Acc: {accuracy:.2f}%, Macro-F1: {macro_f1:.2f}%, Kappa: {kappa:.4f}')
    return avg_loss, accuracy, macro_f1, kappa

def main():
    parser = argparse.ArgumentParser(description='AWN训练脚本')
    parser.add_argument('--config', type=str, default='config.yaml', help='配置文件路径')
    args = parser.parse_args()

    # 如果没有提供任何命令行参数，直接使用默认配置
    if len(sys.argv) == 1:
        print("使用默认配置文件: config.yaml")
        args.config = 'config.yaml'

    # 加载配置
    config = load_config(args.config)

    # 创建输出目录结构
    directories = create_output_directories(config)

    # 设置日志（使用新的logs目录）
    logger = setup_logging(directories['logs'])
    logger.info(f"开始训练，配置文件: {args.config}")
    logger.info(f"数据集类型: {config['data']['dataset_type']}")
    logger.info(f"实验目录: {directories['experiment']}")

    # 保存配置文件副本到configs目录
    config_backup_path = os.path.join(directories['configs'], 'config_backup.yaml')
    with open(config_backup_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    logger.info(f"配置文件备份保存到: {config_backup_path}")

    # 设置设备
    device = torch.device(config['training']['device'] if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")

    # 设置随机种子
    torch.manual_seed(config['training']['seed'])
    np.random.seed(config['training']['seed'])
    
    # 获取数据加载器
    logger.info("加载数据...")
    train_loader, val_loader, test_loader = get_data_loaders(config)
    
    # 创建模型
    logger.info("创建模型...")
    model = create_model(config)
    model = model.to(device)

    # 打印可训练参数数量
    trainable_params, total_params = count_trainable_parameters(model)
    logger.info(f"可训练参数数量: {trainable_params:,}")
    logger.info(f"总参数数量: {total_params:,}")
    logger.info(f"可训练参数比例: {trainable_params/total_params*100:.2f}%")

    # 创建优化器和损失函数
    optimizer = optim.Adam(
        model.parameters(),
        lr=config['training']['learning_rate'],
        weight_decay=config['training']['weight_decay']
    )

    # 使用原始AWN的损失函数
    criterion = AWNLoss(
        lambda1=config['training'].get('lambda_lifting', 0.0079),
        lambda2=config['training'].get('lambda_lifting', 0.0079)
    )

    # 学习率调度器
    if config['training']['scheduler'] == 'cosine':
        scheduler = optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=config['training']['epochs'],
            eta_min=config['training']['min_lr']
        )
    else:
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='max',
            patience=config['training']['patience'],
            factor=0.5
        )
    
    # 训练循环
    best_acc = 0
    best_f1 = 0
    best_kappa = 0
    patience_counter = 0
    training_history = {
        'epochs': [],
        'train_loss': [],
        'train_acc': [],
        'val_loss': [],
        'val_acc': [],
        'val_f1': [],
        'val_kappa': [],
        'epoch_time': [],
        'learning_rate': []
    }

    for epoch in range(config['training']['epochs']):
        epoch_start_time = time.time()
        logger.info(f"Epoch {epoch+1}/{config['training']['epochs']}")

        # 训练
        train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, device, config, logger)

        # 验证
        val_loss, val_acc, val_f1, val_kappa = validate(model, val_loader, criterion, device, logger)

        # 记录训练时间
        epoch_time = time.time() - epoch_start_time
        logger.info(f"Epoch {epoch+1} 训练时间: {epoch_time:.2f} 秒")

        # 记录训练历史
        training_history['epochs'].append(epoch + 1)
        training_history['train_loss'].append(train_loss)
        training_history['train_acc'].append(train_acc)
        training_history['val_loss'].append(val_loss)
        training_history['val_acc'].append(val_acc)
        training_history['val_f1'].append(val_f1)
        training_history['val_kappa'].append(val_kappa)
        training_history['epoch_time'].append(epoch_time)
        training_history['learning_rate'].append(optimizer.param_groups[0]['lr'])

        # 学习率调度
        if config['training']['scheduler'] == 'cosine':
            scheduler.step()
        else:
            scheduler.step(val_acc)

        # 保存最佳模型
        if val_acc > best_acc:
            best_acc = val_acc
            best_f1 = val_f1
            best_kappa = val_kappa
            patience_counter = 0

            # 保存最佳模型到models目录
            best_model_path = os.path.join(directories['models'], 'best_model.pth')
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_acc': best_acc,
                'best_f1': best_f1,
                'best_kappa': best_kappa,
                'config': config,
                'training_history': training_history
            }, best_model_path)

            # 同时保存一个带时间戳的模型副本
            timestamp_model_path = os.path.join(directories['models'], f'model_epoch_{epoch+1}_acc_{val_acc:.2f}.pth')
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_acc': best_acc,
                'best_f1': best_f1,
                'best_kappa': best_kappa,
                'config': config,
                'training_history': training_history
            }, timestamp_model_path)

            logger.info(f"保存最佳模型，验证准确率: {best_acc:.2f}%, Macro-F1: {best_f1:.2f}%, Kappa: {best_kappa:.4f}")
            logger.info(f"模型保存到: {best_model_path}")
            logger.info(f"模型副本保存到: {timestamp_model_path}")
        else:
            patience_counter += 1

        # 早停
        if config['training']['early_stopping'] and patience_counter >= config['training']['early_stop_patience']:
            logger.info(f"早停触发，最佳验证准确率: {best_acc:.2f}%")
            break

    # 保存训练历史到results目录
    history_file = os.path.join(directories['results'], 'training_history.json')
    with open(history_file, 'w') as f:
        json.dump(training_history, f, indent=2)
    logger.info(f"训练历史保存到: {history_file}")

    # 保存训练摘要信息
    total_training_time = sum(training_history['epoch_time'])
    avg_epoch_time = total_training_time / len(training_history['epoch_time'])

    training_summary = {
        'dataset_type': config['data']['dataset_type'],
        'model_type': 'AWN',
        'experiment_timestamp': directories['experiment'].split('_')[-1],
        'total_epochs': len(training_history['epochs']),
        'best_validation_accuracy': float(best_acc),
        'best_macro_f1': float(best_f1),
        'best_kappa': float(best_kappa),
        'total_training_time_seconds': float(total_training_time),
        'total_training_time_minutes': float(total_training_time / 60),
        'average_epoch_time_seconds': float(avg_epoch_time),
        'final_learning_rate': float(optimizer.param_groups[0]['lr']),
        'trainable_parameters': trainable_params,
        'total_parameters': total_params,
        'config_file': args.config
    }

    summary_file = os.path.join(directories['results'], 'training_summary.json')
    with open(summary_file, 'w') as f:
        json.dump(training_summary, f, indent=2)
    logger.info(f"训练摘要保存到: {summary_file}")

    # 输出最终统计信息
    logger.info("训练完成！")
    logger.info(f"最佳验证准确率: {best_acc:.2f}%")
    logger.info(f"最佳Macro-F1: {best_f1:.2f}%")
    logger.info(f"最佳Kappa: {best_kappa:.4f}")
    logger.info(f"总训练时间: {total_training_time:.2f} 秒 ({total_training_time/60:.1f} 分钟)")
    logger.info(f"平均每轮训练时间: {avg_epoch_time:.2f} 秒")
    logger.info(f"实验结果保存在: {directories['experiment']}")

if __name__ == '__main__':
    main()
